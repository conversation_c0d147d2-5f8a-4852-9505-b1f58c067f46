import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { DbAuroraStack } from '../lib/stack/db-aurora-stack';
import * as rds from 'aws-cdk-lib/aws-rds';
import { ShareResourcesStack } from '../lib/stack/share-resources-stack';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

let app: cdk.App;
let stack: DbAuroraStack;


beforeEach(() => {
  app = new cdk.App();
  const envTagName = 'Environment';

  // Create a mock stack for resources that need to be in a Stack scope
  const mockResourceStack = new cdk.Stack(app, 'MockResourceStack', {
    env: getProcEnv(),
  });

  // Create the secret in the mock stack instead of directly under the app
  new secretsmanager.CfnSecret(mockResourceStack, `MockNewRelicSecret`, {});

  // Import output from common CDK app
  const shareResources = new ShareResourcesStack(app, `MockShareResources`, {
    pjPrefix,
    notifyEmail: '<EMAIL>',
    domainPrefix: `${pjPrefix}`.toLowerCase(),
    workspaceId: 'test-workspace',
    channelId: 'test-channel',
    urlForCallback: ['http://test/callback'],
    urlForLogout: ['http://test/logout'],
    myVpcCidr: config.VpcParam.cidr,
    myVpcMaxAzs: config.VpcParam.maxAzs,
    myVpcNatGateways: config.VpcParam.natGateways,
    myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
    logRemovalPolicyParam: config.LogRemovalPolicyParam,
    kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
    env: getProcEnv(),
  });

  const vpc = shareResources.myVpc;
  const appKey = shareResources.appKey;
  const alarmTopic = shareResources.alarmTopic;

  stack = new DbAuroraStack(app, `${pjPrefix}-DBAurora`, {
    pjPrefix,
    vpc: vpc,
    vpcSubnets: vpc.selectSubnets({
      subnetGroupName: 'Protected',
    }),
    appServerSecurityGroup: undefined,
    bastionSecurityGroup: undefined,
    appKey: appKey,
    alarmTopic: alarmTopic,
    ...config.AuroraParam,
    env: getProcEnv(),
    terminationProtection: config.AuroraParam.stackTerminationProtection,
  });

  cdk.Tags.of(app).add(envTagName, envKey);
});

describe('DbAuroraStack', () => {
  test('Should match snapshot', () => {
    const template = Template.fromStack(stack);
    const templateJson = template.toJSON();
    expect(templateJson).toMatchSnapshot();
  });

  test('Should create an Aurora cluster', () => {
    const template = Template.fromStack(stack);

    // Verify exact resource counts
    template.resourceCountIs('AWS::RDS::DBCluster', 1);
    template.resourceCountIs('AWS::CloudWatch::Alarm', 1);
    template.resourceCountIs('AWS::RDS::EventSubscription', 2); // cluster and instance events

    // Verify Aurora cluster is created with correct properties
    template.hasResourceProperties('AWS::RDS::DBCluster', {
      Engine: Match.stringLikeRegexp('aurora-(mysql|postgresql)'),
      EngineVersion: '8.0.mysql_aurora.3.04.3',
      DatabaseName: config.AuroraParam.dbName,
      MasterUsername: config.AuroraParam.dbUser,
      DeletionProtection: config.AuroraParam.stackTerminationProtection,
      BackupRetentionPeriod: config.AuroraParam.backupRetentionDays,
      PreferredBackupWindow: config.AuroraParam.backupPreferredWindow,
      PreferredMaintenanceWindow: config.AuroraParam.preferredMaintenanceWindow,
      EnableCloudwatchLogsExports: ['error', 'general', 'slowquery', 'audit'],
      StorageEncrypted: true,
    });

    // Verify DB instances are created with correct properties
    template.hasResourceProperties('AWS::RDS::DBInstance', {
      AutoMinorVersionUpgrade: config.AuroraParam.autoMinorVersionUpgrade,
      EnablePerformanceInsights: config.AuroraParam.enablePerformanceInsights,
    });

    // Verify CloudWatch alarm is created for CPU utilization
    template.hasResourceProperties('AWS::CloudWatch::Alarm', {
      MetricName: 'CPUUtilization',
      Namespace: 'AWS/RDS',
      ComparisonOperator: 'GreaterThanOrEqualToThreshold',
      Threshold: 90,
    });

    // Verify event subscriptions are created
    template.hasResourceProperties('AWS::RDS::EventSubscription', {
      SourceType: 'db-cluster',
      EventCategories: ['failure', 'failover', 'maintenance'],
    });

    template.hasResourceProperties('AWS::RDS::EventSubscription', {
      SourceType: 'db-instance',
      EventCategories: ['availability', 'configuration change', 'deletion', 'failover', 'failure', 'maintenance', 'notification', 'recovery'],
    });
  });

  // Test Aurora PostgreSQL engine
  test('Should create Aurora PostgreSQL cluster when dbVersion is postgres engine', () => {
    // Create a new app for this test to avoid conflicts
    const postgresApp = new cdk.App();
    const postgresEnvTagName = 'Environment';
    cdk.Tags.of(postgresApp).add(postgresEnvTagName, envKey);

    // Create a mock stack for PostgreSQL test resources
    new cdk.Stack(postgresApp, 'PostgresMockStack', {
      env: getProcEnv(),
    });

    // Create a new ShareResourcesStack for this test
    const postgresShareResources = new ShareResourcesStack(postgresApp, `PostgresMockShareResources`, {
      pjPrefix,
      notifyEmail: '<EMAIL>',
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: 'test-workspace',
      channelId: 'test-channel',
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });

    const vpc = postgresShareResources.myVpc;
    const appKey = postgresShareResources.appKey;
    const alarmTopic = postgresShareResources.alarmTopic;

    const stackPostgres = new DbAuroraStack(postgresApp, `${pjPrefix}-DBAurora-postgress`, {
      pjPrefix,
      vpc: vpc,
      vpcSubnets: vpc.selectSubnets({
        subnetGroupName: 'Protected',
      }),
      // appServerSecurityGroup: ecs.app.backEcsApps.securityGroupForFargate,
      // bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
      appServerSecurityGroup: undefined, // TODO: remove later
      bastionSecurityGroup: undefined, // TODO: remove later
      appKey: appKey,
      alarmTopic: alarmTopic,
      ...config.AuroraParam,
      dbVersion: rds.AuroraPostgresEngineVersion.VER_15_4, // This is the key property that needs to be fixed
      env: getProcEnv(),
      terminationProtection: config.AuroraParam.stackTerminationProtection,
    });

    // Assert
    const template = Template.fromStack(stackPostgres);

    // Verify PostgreSQL engine is used
    template.hasResourceProperties('AWS::RDS::DBCluster', {
      Engine: 'aurora-postgresql',
      EngineVersion: '15.4',
    });

    // Verify resource counts are correct
    template.resourceCountIs('AWS::RDS::DBCluster', 1);
    template.resourceCountIs('AWS::CloudWatch::Alarm', 1);
  });

  // test property enablePerformanceInsights = true
  // create new stack for this test to avoid conflicts
  test('Should enable Performance Insights when enablePerformanceInsights is true', () => {
    // Create a new app for this test to avoid conflicts
    const postgresApp = new cdk.App();
    const postgresEnvTagName = 'Environment';
    cdk.Tags.of(postgresApp).add(postgresEnvTagName, envKey);

    // Create a mock stack for PostgreSQL test resources
    new cdk.Stack(postgresApp, 'PostgresMockStack', {
      env: getProcEnv(),
    });

    // Create a new ShareResourcesStack for this test
    const postgresShareResources = new ShareResourcesStack(postgresApp, `PostgresMockShareResources`, {
      pjPrefix,
      notifyEmail: '<EMAIL>',
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: 'test-workspace',
      channelId: 'test-channel',
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });

    const vpc = postgresShareResources.myVpc;
    const appKey = postgresShareResources.appKey;
    const alarmTopic = postgresShareResources.alarmTopic;
    // mock appServerSecurityGroup is SecurityGroup
    const appServerSecurityGroup = new cdk.aws_ec2.SecurityGroup(postgresShareResources, 'MockAppServerSecurityGroup', {
      vpc: vpc,
      allowAllOutbound: true,
      description: 'Mock App Server Security Group',
    });

    const bastionSecurityGroup= new cdk.aws_ec2.SecurityGroup(postgresShareResources, 'MockBastionSecurityGroup', {
      vpc: vpc,
      allowAllOutbound: true,
      description: 'Mock Bastion Security Group',
    });
    // Create a new ShareResourcesStack for this test
    const stackPostgres = new DbAuroraStack(postgresApp, `${pjPrefix}-DBAurora-postgress`, {
      pjPrefix,
      vpc: vpc,
      vpcSubnets: vpc.selectSubnets({
        subnetGroupName: 'Protected',
      }),
      // appServerSecurityGroup: ecs.app.backEcsApps.securityGroupForFargate,
      // bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
      appKey: appKey,
      alarmTopic: alarmTopic,
      ...config.AuroraParam,
      dbVersion: rds.AuroraMysqlEngineVersion.VER_3_04_3, // This is the key property that needs to be fixed
      // Override readers to also enable Performance Insights to avoid validation conflicts
      readers: config.AuroraParam.readers?.map(reader => ({
        ...reader,
        enablePerformanceInsights: true,
      })),
      env: getProcEnv(),
      terminationProtection: config.AuroraParam.stackTerminationProtection,
      enablePerformanceInsights: true, // Enable Performance Insights for this test
      autoMinorVersionUpgrade: undefined,
      backupPreferredWindow: undefined,
      backupRetentionDays: undefined,
      preferredMaintenanceWindow: undefined,
      deletionProtection: undefined,
      appServerSecurityGroup: appServerSecurityGroup,
      bastionSecurityGroup: bastionSecurityGroup,
    });

    const template = Template.fromStack(stackPostgres);
    template.hasResourceProperties('AWS::RDS::DBInstance', {
      EnablePerformanceInsights: true, // Check if Performance Insights is enabled
    });

    // Verify basic cluster properties are still correct
    template.resourceCountIs('AWS::RDS::DBCluster', 1);
    template.resourceCountIs('AWS::CloudWatch::Alarm', 1);
  });

  test('Should match expected snapshot when aurora serverless stack is created', () => {
    // Arrange
    const serverlessApp = new cdk.App();
    cdk.Tags.of(serverlessApp).add('Environment', envKey);

    // Create a new ShareResourcesStack for serverless test
    const serverlessShareResources = new ShareResourcesStack(serverlessApp, `ServerlessMockShareResources`, {
      pjPrefix,
      notifyEmail: '<EMAIL>',
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: 'test-workspace',
      channelId: 'test-channel',
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });

    // Act - Create serverless stack
    const serverlessStack = new DbAuroraStack(serverlessApp, `${pjPrefix}-DBAurora-serverless`, {
      pjPrefix,
      vpc: serverlessShareResources.myVpc,
      vpcSubnets: serverlessShareResources.myVpc.selectSubnets({
        subnetGroupName: 'Protected',
      }),
      appServerSecurityGroup: undefined,
      bastionSecurityGroup: undefined,
      appKey: serverlessShareResources.appKey,
      alarmTopic: serverlessShareResources.alarmTopic,
      ...config.AuroraParam,
      auroraType: 'SERVERLESS', // Override to use serverless
      serverless: {
        auroraMinAcu: 2,
        auroraMaxAcu: 16,
        scaleWithWriter: false,
      },
      readers: [], // No readers for basic serverless test
      env: getProcEnv(),
      terminationProtection: config.AuroraParam.stackTerminationProtection,
    });

    // Assert - Generate snapshot
    const template = Template.fromStack(serverlessStack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  // Test cases based on the actual serverless snapshot
  test('Should create aurora serverless when serverless configuration is provided', () => {
    // Arrange
    const serverlessApp = new cdk.App();
    cdk.Tags.of(serverlessApp).add('Environment', envKey);

    const serverlessShareResources = new ShareResourcesStack(serverlessApp, `ServerlessTestMockShareResources`, {
      pjPrefix,
      notifyEmail: '<EMAIL>',
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: 'test-workspace',
      channelId: 'test-channel',
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });
    const vpc = serverlessShareResources.myVpc;
    const appKey = serverlessShareResources.appKey;
    const alarmTopic = serverlessShareResources.alarmTopic;

    // Act
    const serverlessStack = new DbAuroraStack(serverlessApp, `${pjPrefix}-DBAurora-serverless-test`, {
      pjPrefix,
      vpc,
      vpcSubnets: serverlessShareResources.myVpc.selectSubnets({
        subnetGroupName: 'Protected',
      }),
      appServerSecurityGroup: undefined,
      bastionSecurityGroup: undefined,
      appKey,
      alarmTopic,
      ...config.AuroraParam,
      auroraType: 'SERVERLESS',
      serverless: {
        auroraMinAcu: 2,
        auroraMaxAcu: 16,
        scaleWithWriter: false,
      },
      readers: [],
      env: getProcEnv(),
      terminationProtection: config.AuroraParam.stackTerminationProtection,
    });

    // Assert - Based on actual snapshot data
    const template = Template.fromStack(serverlessStack);

    // Verify exact resource counts from snapshot
    template.resourceCountIs('AWS::RDS::DBCluster', 1);
    template.resourceCountIs('AWS::CloudWatch::Alarm', 1);
    template.resourceCountIs('AWS::RDS::EventSubscription', 2);
    template.resourceCountIs('AWS::RDS::DBClusterParameterGroup', 1);
    template.resourceCountIs('AWS::RDS::DBParameterGroup', 1);

    // Verify Aurora cluster properties from snapshot
    template.hasResourceProperties('AWS::RDS::DBCluster', {
      Engine: 'aurora-mysql',
      EngineVersion: '8.0.mysql_aurora.3.04.3',
      DatabaseName: 'mydbname',
      MasterUsername: 'dbUser',
      BackupRetentionPeriod: 1,
      PreferredBackupWindow: '17:00-18:00',
      PreferredMaintenanceWindow: 'Wed:19:00-Wed:20:00',
      DeletionProtection: false,
      StorageEncrypted: true,
      EnableCloudwatchLogsExports: ['error', 'general', 'slowquery', 'audit'],
      BacktrackWindow: 86400,
      CopyTagsToSnapshot: true,
      ServerlessV2ScalingConfiguration: {
        MinCapacity: 2,
        MaxCapacity: 16,
      },
    });

    // Verify CloudWatch alarm properties from snapshot
    template.hasResourceProperties('AWS::CloudWatch::Alarm', {
      MetricName: 'CPUUtilization',
      Namespace: 'AWS/RDS',
      ComparisonOperator: 'GreaterThanOrEqualToThreshold',
      Threshold: 90,
      EvaluationPeriods: 3,
      DatapointsToAlarm: 3,
      Period: 60,
      Statistic: 'Average',
      ActionsEnabled: true,
    });

    // Verify event subscriptions from snapshot
    template.hasResourceProperties('AWS::RDS::EventSubscription', {
      SourceType: 'db-cluster',
      EventCategories: ['failure', 'failover', 'maintenance'],
      Enabled: true,
    });

    template.hasResourceProperties('AWS::RDS::EventSubscription', {
      SourceType: 'db-instance',
      EventCategories: ['availability', 'configuration change', 'deletion', 'failover', 'failure', 'maintenance', 'notification', 'recovery'],
      Enabled: true,
    });

    // Verify cluster parameter group from snapshot
    template.hasResourceProperties('AWS::RDS::DBClusterParameterGroup', {
      Description: 'Aurora Cluster Parameter Group',
      Family: 'aurora-mysql8.0',
      Parameters: {
        binlog_format: 'ROW',
        character_set_client: 'utf8mb4',
        character_set_connection: 'utf8mb4',
        character_set_database: 'utf8mb4',
        character_set_results: 'utf8mb4',
        character_set_server: 'utf8mb4',
        init_connect: 'SET NAMES utf8mb4',
        time_zone: 'Asia/Tokyo',
      },
    });

    // Verify instance parameter group from snapshot
    template.hasResourceProperties('AWS::RDS::DBParameterGroup', {
      Description: 'Aurora Instance Parameter Group',
      Family: 'aurora-mysql8.0',
      Parameters: {
        long_query_time: '10',
        slow_query_log: '1',
      },
    });
  });

  test('Should Create Aurora Cluster With RDS Proxy When EnableRdsProxy Is True', () => {
    // Arrange
    const proxyApp = new cdk.App();
    cdk.Tags.of(proxyApp).add('Environment', envKey);

    const proxyShareResources = new ShareResourcesStack(proxyApp, `ProxyMockShareResources`, {
      pjPrefix,
      notifyEmail: '<EMAIL>',
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: 'test-workspace',
      channelId: 'test-channel',
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });

    // Create mock security groups for app server and bastion
    const mockAppServerSG = new cdk.aws_ec2.SecurityGroup(proxyShareResources, 'MockAppServerSG', {
      vpc: proxyShareResources.myVpc,
      description: 'Mock App Server Security Group',
    });

    const mockBastionSG = new cdk.aws_ec2.SecurityGroup(proxyShareResources, 'MockBastionSG', {
      vpc: proxyShareResources.myVpc,
      description: 'Mock Bastion Security Group',
    });
    const vpc = proxyShareResources.myVpc;
    const appKey = proxyShareResources.appKey;
    const alarmTopic = proxyShareResources.alarmTopic;

    // Act
    const proxyStack = new DbAuroraStack(proxyApp, `${pjPrefix}-DBAurora-proxy`, {
      pjPrefix,
      vpc,
      vpcSubnets: proxyShareResources.myVpc.selectSubnets({
        subnetGroupName: 'Protected',
      }),
      appServerSecurityGroup: mockAppServerSG,
      bastionSecurityGroup: mockBastionSG,
      appKey,
      alarmTopic,
      ...config.AuroraParam,
      enableRdsProxy: true, // Enable RDS Proxy
      rdsProxyConfig: {
        maxConnectionsPercent: 80,
        maxIdleConnectionsPercent: 30,
        requireTLS: true,
        idleClientTimeout: 3600,
      },
      env: getProcEnv(),
      terminationProtection: config.AuroraParam.stackTerminationProtection,
    });

    // Assert
    const template = Template.fromStack(proxyStack);

    // Verify RDS Proxy is created
    template.resourceCountIs('AWS::RDS::DBProxy', 1);
    template.resourceCountIs('AWS::RDS::DBProxyTargetGroup', 1);

    // Verify RDS Proxy properties (based on actual CloudFormation template structure)
    template.hasResourceProperties('AWS::RDS::DBProxy', {
      DBProxyName: `${pjPrefix}-AuroraProxy`, // Note: CDK adds dash between prefix and name
      EngineFamily: 'MYSQL',
      RequireTLS: true,
      IdleClientTimeout: 3600,
    });

    // Verify proxy security group is created (cluster SG + proxy SG = 2 total)
    template.resourceCountIs('AWS::EC2::SecurityGroup', 2);

    // Verify basic cluster resources are still created
    template.resourceCountIs('AWS::RDS::DBCluster', 1);
    template.resourceCountIs('AWS::CloudWatch::Alarm', 1);
  });

  test('Should Create Aurora Cluster With Different Instance Types When Custom Instance Types Are Provided', () => {
    // Arrange
    const customInstanceApp = new cdk.App();
    cdk.Tags.of(customInstanceApp).add('Environment', envKey);

    const customInstanceShareResources = new ShareResourcesStack(customInstanceApp, `CustomInstanceMockShareResources`, {
      pjPrefix,
      notifyEmail: '<EMAIL>',
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: 'test-workspace',
      channelId: 'test-channel',
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });
    const vpc = customInstanceShareResources.myVpc;
    const appKey = customInstanceShareResources.appKey;
    const alarmTopic = customInstanceShareResources.alarmTopic;
    // Act
    const customInstanceStack = new DbAuroraStack(customInstanceApp, `${pjPrefix}-DBAurora-custom-instance`, {
      pjPrefix,
      vpc,
      vpcSubnets: customInstanceShareResources.myVpc.selectSubnets({
        subnetGroupName: 'Protected',
      }),
      appServerSecurityGroup: undefined,
      bastionSecurityGroup: undefined,
      appKey,
      alarmTopic,
      ...config.AuroraParam,
      writerInstanceType: cdk.aws_ec2.InstanceType.of(cdk.aws_ec2.InstanceClass.R6G, cdk.aws_ec2.InstanceSize.LARGE),
      readers: [
        {
          instanceType: cdk.aws_ec2.InstanceType.of(cdk.aws_ec2.InstanceClass.R6G, cdk.aws_ec2.InstanceSize.MEDIUM),
          enablePerformanceInsights: true,
          autoMinorVersionUpgrade: true,
        },
        {
          instanceType: cdk.aws_ec2.InstanceType.of(cdk.aws_ec2.InstanceClass.R6G, cdk.aws_ec2.InstanceSize.SMALL),
          enablePerformanceInsights: false,
          autoMinorVersionUpgrade: false,
        },
      ],
      env: getProcEnv(),
      terminationProtection: config.AuroraParam.stackTerminationProtection,
    });

    // Assert
    const template = Template.fromStack(customInstanceStack);

    // Verify correct number of instances (1 writer + 2 readers)
    template.resourceCountIs('AWS::RDS::DBInstance', 3);

    // Verify writer instance properties
    template.hasResourceProperties('AWS::RDS::DBInstance', {
      DBInstanceClass: 'db.r6g.large',
      Engine: 'aurora-mysql',
    });

    // Verify reader instances with different configurations
    template.hasResourceProperties('AWS::RDS::DBInstance', {
      DBInstanceClass: 'db.r6g.medium',
      EnablePerformanceInsights: true,
      AutoMinorVersionUpgrade: true,
    });

    template.hasResourceProperties('AWS::RDS::DBInstance', {
      DBInstanceClass: 'db.r6g.small',
      EnablePerformanceInsights: false,
      AutoMinorVersionUpgrade: false,
    });

    // Verify basic cluster resources
    template.resourceCountIs('AWS::RDS::DBCluster', 1);
    template.resourceCountIs('AWS::CloudWatch::Alarm', 1);
  });

  test('Should Create Aurora Cluster With Custom Backup Configuration When Custom Backup Settings Are Provided', () => {
    // Arrange
    const backupApp = new cdk.App();
    cdk.Tags.of(backupApp).add('Environment', envKey);

    const backupShareResources = new ShareResourcesStack(backupApp, `BackupMockShareResources`, {
      pjPrefix,
      notifyEmail: '<EMAIL>',
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: 'test-workspace',
      channelId: 'test-channel',
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });
    const vpc = backupShareResources.myVpc;
    const appKey = backupShareResources.appKey;
    const alarmTopic = backupShareResources.alarmTopic;
    // Act
    const backupStack = new DbAuroraStack(backupApp, `${pjPrefix}-DBAurora-backup`, {
      pjPrefix,
      vpc,
      vpcSubnets: backupShareResources.myVpc.selectSubnets({
        subnetGroupName: 'Protected',
      }),
      appServerSecurityGroup: undefined,
      bastionSecurityGroup: undefined,
      appKey,
      alarmTopic,
      ...config.AuroraParam,
      backupRetentionDays: 14, // Custom backup retention
      backupPreferredWindow: '03:00-04:00', // Custom backup window
      preferredMaintenanceWindow: 'Sun:04:00-Sun:05:00', // Custom maintenance window
      deletionProtection: true, // Enable deletion protection
      env: getProcEnv(),
      terminationProtection: true, // Enable termination protection
    });

    // Assert
    const template = Template.fromStack(backupStack);

    // Verify custom backup configuration
    template.hasResourceProperties('AWS::RDS::DBCluster', {
      BackupRetentionPeriod: 14,
      PreferredBackupWindow: '03:00-04:00',
      PreferredMaintenanceWindow: 'Sun:04:00-Sun:05:00',
      DeletionProtection: true,
    });

    // Verify basic cluster resources
    template.resourceCountIs('AWS::RDS::DBCluster', 1);
    template.resourceCountIs('AWS::CloudWatch::Alarm', 1);
  });

  test('Should Create Aurora Cluster With Google Identity Provider When Cognito Google OAuth Is Configured', () => {
    // Arrange
    const googleApp = new cdk.App();
    cdk.Tags.of(googleApp).add('Environment', envKey);

    // Create a mock secret for Google OAuth credentials
    const mockSecretStack = new cdk.Stack(googleApp, 'MockSecretStack', {
      env: getProcEnv(),
    });

    const mockGoogleSecret = new cdk.aws_secretsmanager.Secret(mockSecretStack, 'MockGoogleOAuthSecret', {
      description: 'Mock Google OAuth credentials for testing',
      generateSecretString: {
        secretStringTemplate: JSON.stringify({ client_id: 'mock-client-id' }),
        generateStringKey: 'client_secret',
        excludeCharacters: '"@/\\',
      },
    });

    // Create a custom stack to test Cognito with Google identity provider
    const cognitoTestStack = new cdk.Stack(googleApp, 'CognitoTestStack', {
      env: getProcEnv(),
    });

    // Import Cognito construct directly to test Google identity provider branch
    const { Cognito } = require('../lib/construct/cognito-construct');

    // Create Cognito with Google identity provider to test the uncovered branch
    const cognitoWithGoogle = new Cognito(cognitoTestStack, 'CognitoWithGoogle', {
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      identityProvider: cdk.aws_cognito.UserPoolClientIdentityProvider.GOOGLE,
      secretArn: mockGoogleSecret.secretArn,
    });

    // Create regular ShareResourcesStack for Aurora testing
    const regularShareResources = new ShareResourcesStack(googleApp, `RegularMockShareResources`, {
      pjPrefix,
      notifyEmail: '<EMAIL>',
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: 'test-workspace',
      channelId: 'test-channel',
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });
    const vpc = regularShareResources.myVpc;
    const appKey = regularShareResources.appKey;
    const alarmTopic = regularShareResources.alarmTopic;

    // Act - Create Aurora stack with regular resources
    const googleStack = new DbAuroraStack(googleApp, `${pjPrefix}-DBAurora-google`, {
      pjPrefix,
      vpc,
      vpcSubnets: regularShareResources.myVpc.selectSubnets({
        subnetGroupName: 'Protected',
      }),
      appServerSecurityGroup: undefined,
      bastionSecurityGroup: undefined,
      appKey,
      alarmTopic,
      ...config.AuroraParam,
      env: getProcEnv(),
      terminationProtection: config.AuroraParam.stackTerminationProtection,
    });

    // Assert
    const auroraTemplate = Template.fromStack(googleStack);
    const cognitoTemplate = Template.fromStack(cognitoTestStack);

    // Verify Aurora cluster is created normally
    auroraTemplate.resourceCountIs('AWS::RDS::DBCluster', 1);
    auroraTemplate.resourceCountIs('AWS::CloudWatch::Alarm', 1);

    // Verify Google Identity Provider is created in Cognito test stack (this tests the uncovered branch)
    cognitoTemplate.resourceCountIs('AWS::Cognito::UserPoolIdentityProvider', 1);

    // Verify Google Identity Provider properties
    cognitoTemplate.hasResourceProperties('AWS::Cognito::UserPoolIdentityProvider', {
      ProviderName: 'Google',
      ProviderType: 'Google',
      AttributeMapping: {
        email: 'email',
      },
    });

    // Verify Cognito User Pool and Client are created
    cognitoTemplate.resourceCountIs('AWS::Cognito::UserPool', 1);
    cognitoTemplate.resourceCountIs('AWS::Cognito::UserPoolClient', 1);
    cognitoTemplate.resourceCountIs('AWS::Cognito::UserPoolDomain', 1);

    // Verify the Cognito construct has the expected properties
    expect(cognitoWithGoogle.userPool).toBeDefined();
    expect(cognitoWithGoogle.props.identityProvider).toBe(cdk.aws_cognito.UserPoolClientIdentityProvider.GOOGLE);
    expect(cognitoWithGoogle.props.secretArn).toBe(mockGoogleSecret.secretArn);
  });
});
